/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AccountManagementModal: typeof import('./components/AccountManagementModal.vue')['default']
    AccountManager: typeof import('./components/AccountManager.vue')['default']
    AutoReply: typeof import('./components/AutoReply.vue')['default']
    BaseButton: typeof import('./components/common/BaseButton.vue')['default']
    BaseHeader: typeof import('./components/layouts/BaseHeader.vue')['default']
    BaseModal: typeof import('./components/common/BaseModal.vue')['default']
    BaseSide: typeof import('./components/layouts/BaseSide.vue')['default']
    ChatInterface: typeof import('./components/ChatInterface.vue')['default']
    ContactInfoTest: typeof import('./components/ContactInfoTest.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElResult: typeof import('element-plus/es')['ElResult']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElStatistic: typeof import('element-plus/es')['ElStatistic']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    EmojiImage: typeof import('./components/common/EmojiImage.vue')['default']
    FeatureProxyManagement: typeof import('./components/feature/Proxy/FeatureProxyManagement.vue')['default']
    FileCacheManager: typeof import('./components/business/FileCacheManager.vue')['default']
    FileMessage: typeof import('./components/common/FileMessage.vue')['default']
    FriendManagement: typeof import('./components/FriendManagement.vue')['default']
    FriendManagement_backup: typeof import('./components/FriendManagement_backup.vue')['default']
    FriendRequestNotification: typeof import('./components/FriendRequestNotification.vue')['default']
    GroupManagement: typeof import('./components/GroupManagement.vue')['default']
    HelloWorld: typeof import('./components/HelloWorld.vue')['default']
    ImageMessage: typeof import('./components/common/ImageMessage.vue')['default']
    LoadingSpinner: typeof import('./components/common/LoadingSpinner.vue')['default']
    LoginForm: typeof import('./components/LoginForm.vue')['default']
    Logos: typeof import('./components/Logos.vue')['default']
    MessageBoxDemo: typeof import('./components/MessageBoxDemo.vue')['default']
    MessageItem: typeof import('./components/business/MessageItem.vue')['default']
    PresetFileCacheManager: typeof import('./components/business/PresetFileCacheManager.vue')['default']
    ProxyFilter: typeof import('./components/feature/Proxy/components/ProxyFilter.vue')['default']
    ProxyManagement: typeof import('./components/business/ProxyManagement.vue')['default']
    ProxyStats: typeof import('./components/feature/Proxy/components/ProxyStats.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Table: typeof import('./components/base/Table/index.vue')['default']
    VideoMessage: typeof import('./components/common/VideoMessage.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
