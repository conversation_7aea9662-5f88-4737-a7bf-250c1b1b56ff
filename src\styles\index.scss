// import dark theme
@use 'element-plus/theme-chalk/src/dark/css-vars.scss' as *;

// 导入自定义样式系统
@use './variables.scss';
@use './utilities.scss';

// :root {
//   --ep-color-primary: red;
// }

body {
  font-family: Inter, system-ui, Avenir, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
}

a {
  color: var(--ep-color-primary);
}

code {
  border-radius: 2px;
  padding: 2px 4px;
  background-color: var(--ep-color-primary-light-9);
  color: var(--ep-color-primary);
}

#nprogress {
  pointer-events: none;
}

#nprogress .bar {
  background: rgb(13, 148, 136);
  opacity: 0.75;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
}


